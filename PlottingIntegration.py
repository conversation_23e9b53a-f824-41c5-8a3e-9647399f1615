"""
Dimension-Aware Plotting System
This module provides utilities to launch the appropriate plotting window (2D or 3D)
based on data dimensionality, closely replicating the original plot3D and PlottingApp interfaces.
"""

import tkinter as tk
from tkinter import messagebox
import pandas as pd
import numpy as np
import os

class DimensionDetector:
    """Advanced dimension detection for scientific data"""

    @staticmethod
    def detect_dimension_from_filename(filename):
        """Detect dimension from filename suffix (_1D, _2D, _3D)"""
        if '_1D' in filename:
            return 1
        elif '_2D' in filename:
            return 2
        elif '_3D' in filename:
            return 3
        return None

    @staticmethod
    def detect_dimension_from_data_structure(data):
        """Detect dimension based on data structure analysis"""
        if not isinstance(data, pd.DataFrame):
            return 2  # Default fallback

        rows, cols = data.shape

        # 1D detection: Single column or row of data
        if cols == 1 or rows == 1:
            return 1

        # 3D detection heuristics
        # Check if it looks like a 3D matrix format (X values in first row, Y values in first column)
        try:
            # Skip first cell (0,0) and check if first row and column are numeric coordinates
            if rows > 2 and cols > 2:
                # Check first row (excluding first cell) - should be X coordinates
                first_row_data = data.iloc[0, 1:].values
                first_row_numeric = pd.to_numeric(first_row_data, errors='coerce')
                first_row_valid = not np.isnan(first_row_numeric).any()

                # Check first column (excluding first cell) - should be Y coordinates
                first_col_data = data.iloc[1:, 0].values
                first_col_numeric = pd.to_numeric(first_col_data, errors='coerce')
                first_col_valid = not np.isnan(first_col_numeric).any()

                # Check if remaining data looks like Z values (numeric matrix)
                z_data = data.iloc[1:, 1:].values
                z_numeric = pd.to_numeric(z_data.flatten(), errors='coerce')
                z_valid = not np.isnan(z_numeric).any()

                if first_row_valid and first_col_valid and z_valid:
                    return 3
        except:
            pass

        # Large number of columns might indicate 3D matrix format
        if cols > 15:
            return 3

        # Default to 2D for multi-column data
        return 2

    @staticmethod
    def detect_dimension(data, filename=None):
        """Main dimension detection method"""
        # First try filename detection
        if filename:
            dim_from_filename = DimensionDetector.detect_dimension_from_filename(filename)
            if dim_from_filename:
                return dim_from_filename

        # Fall back to data structure analysis
        return DimensionDetector.detect_dimension_from_data_structure(data)

class PlottingLauncher:
    """Utility class for launching appropriate plotting windows based on data dimensionality"""
    
    @staticmethod
    def launch_plotting_window(data, dimension=None, title="Plotting Window", config=None, filename=None, project_callback=None):
        """Launch appropriate plotting window based on data dimensionality"""
        try:
            # Auto-detect dimension if not specified
            if dimension is None:
                dimension = DimensionDetector.detect_dimension(data, filename)

            # Create new window
            plot_window = tk.Toplevel()
            plot_window.title(f"{title} - {dimension}D Data")

            # Launch appropriate plotting window based on dimension
            if dimension == 2:
                from PlottingApp2D import Plot2DWindow
                plotting_app = Plot2DWindow(plot_window, data, config or {}, project_callback)
            elif dimension == 3:
                from PlottingApp3D import Plot3DWindow
                plotting_app = Plot3DWindow(plot_window, data, config or {}, project_callback)
            else:
                messagebox.showwarning("Warning", f"Unsupported dimension: {dimension}D. Defaulting to 2D.")
                from PlottingApp2D import Plot2DWindow
                plotting_app = Plot2DWindow(plot_window, data, config or {}, project_callback)

            return plotting_app

        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch plotting window: {str(e)}")
            return None
    
    @staticmethod
    def launch_from_file(file_path, dimension=None, title=None):
        """Launch plotting window directly from a file"""
        try:
            # Load data from file
            if file_path.endswith('.csv'):
                data = pd.read_csv(file_path)
            elif file_path.endswith(('.txt', '.dat')):
                data = pd.read_csv(file_path, sep=None, engine='python')
            else:
                raise ValueError(f"Unsupported file format: {file_path}")
            
            # Generate title if not provided
            if title is None:
                title = f"Plot - {os.path.basename(file_path)}"
            
            return PlottingLauncher.launch_plotting_window(data, dimension, title)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load and plot file: {str(e)}")
            return None

class ProcessingWizardIntegration:
    """Integration utilities for ProcessingWizard"""
    
    @staticmethod
    def add_plotting_button_to_wizard(wizard_instance):
        """Add a plotting button to an existing ProcessingWizard instance"""
        try:
            # Find the processing buttons frame
            for widget in wizard_instance.root.winfo_children():
                if isinstance(widget, tk.Frame):
                    # Add plotting button
                    plot_btn = tk.Button(
                        widget,
                        text="Open in Plotting Window",
                        command=lambda: ProcessingWizardIntegration.launch_from_wizard(wizard_instance),
                        bg='lightblue',
                        width=20
                    )
                    plot_btn.pack(pady=2, padx=5, fill=tk.X)
                    break
        except Exception as e:
            print(f"Failed to add plotting button: {e}")
    
    @staticmethod
    def launch_from_wizard(wizard_instance):
        """Launch plotting window from ProcessingWizard data"""
        if not hasattr(wizard_instance, 'current_data') or wizard_instance.current_data is None:
            messagebox.showwarning("Warning", "No data loaded in wizard")
            return
        
        try:
            # Get data and dimension from wizard
            data = wizard_instance.current_data.copy()
            dimension = getattr(wizard_instance, 'data_dimension', None)
            
            # Convert dimension string to int if needed
            if isinstance(dimension, str):
                if '2D' in dimension:
                    dimension = 2
                elif '3D' in dimension:
                    dimension = 3
                else:
                    dimension = None
            
            # Launch plotting window
            title = f"Plot - {getattr(wizard_instance, 'file_path', 'Wizard Data')}"
            return PlottingLauncher.launch_plotting_window(data, dimension, title)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch plotting from wizard: {str(e)}")

# Example usage functions
def demo_2d_plotting():
    """Demo function for 2D plotting"""
    # Create sample 2D data
    x = np.linspace(0, 10, 100)
    data = pd.DataFrame({
        'x': x,
        'sin(x)': np.sin(x),
        'cos(x)': np.cos(x),
        'tan(x)': np.tan(x)
    })
    
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    PlottingLauncher.launch_plotting_window(data, dimension=2, title="2D Demo")
    root.mainloop()

def demo_3d_plotting():
    """Demo function for 3D plotting"""
    # Create sample 3D data
    x = np.linspace(-5, 5, 20)
    y = np.linspace(-5, 5, 15)
    X, Y = np.meshgrid(x, y)
    Z = np.sin(np.sqrt(X**2 + Y**2))
    
    # Create DataFrame in matrix format
    df = pd.DataFrame(Z)
    # Add x values as first row
    df.loc[-1] = [0] + list(x)
    df.index = df.index + 1
    df.sort_index(inplace=True)
    # Add y values as first column
    df.insert(0, 'y_axis', [0] + list(y))
    
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    PlottingLauncher.launch_plotting_window(df, dimension=3, title="3D Demo")
    root.mainloop()

def demo_file_plotting():
    """Demo function for plotting from file"""
    from tkinter import filedialog
    
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    file_path = filedialog.askopenfilename(
        title="Select Data File",
        filetypes=[
            ("CSV files", "*.csv"),
            ("Text files", "*.txt"),
            ("Data files", "*.dat"),
            ("All files", "*.*")
        ]
    )
    
    if file_path:
        PlottingLauncher.launch_from_file(file_path)
        root.mainloop()
    else:
        root.destroy()

if __name__ == "__main__":
    # Demo menu
    root = tk.Tk()
    root.title("Plotting Integration Demo")
    root.geometry("300x200")
    
    tk.Label(root, text="Unified Plotting Window Demo", font=('Arial', 14, 'bold')).pack(pady=10)
    
    tk.Button(root, text="Demo 2D Plotting", command=demo_2d_plotting, width=20).pack(pady=5)
    tk.Button(root, text="Demo 3D Plotting", command=demo_3d_plotting, width=20).pack(pady=5)
    tk.Button(root, text="Plot from File", command=demo_file_plotting, width=20).pack(pady=5)
    tk.Button(root, text="Exit", command=root.quit, width=20).pack(pady=5)
    
    root.mainloop()
