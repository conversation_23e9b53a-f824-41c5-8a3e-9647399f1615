"""
Advanced Data Processing Methods Module

This module contains comprehensive data processing methods for scientific data analysis.
Includes advanced features like cropping, baseline correction, FFT, deconvolution,
peak integration, and custom formula calculations.

Each method is designed to work with pandas DataFrames and return processed data.
"""

import pandas as pd
import numpy as np
import warnings

# Optional imports with fallbacks
try:
    from scipy import signal, interpolate, stats
    HAS_SCIPY = True
except ImportError:
    HAS_SCIPY = False
    warnings.warn("SciPy not available. Some processing features may be limited.")

try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    warnings.warn("Scikit-learn not available. Some scaling features may be limited.")

class DataProcessor:
    """Class containing all data processing methods"""
    
    @staticmethod
    def normalize_data(data, method='min_max', column=None, custom_range=None):
        """
        Normalize data using various methods
        
        Args:
            data: pandas DataFrame
            method: 'min_max', 'z_score', 'custom'
            column: specific column to normalize (None for all numeric columns)
            custom_range: tuple (min, max) for custom normalization
        
        Returns:
            pandas DataFrame with normalized data
        """
        result = data.copy()
        
        if column:
            columns_to_process = [column] if column in data.columns else []
        else:
            columns_to_process = data.select_dtypes(include=[np.number]).columns
        
        for col in columns_to_process:
            if method == 'min_max':
                if HAS_SKLEARN:
                    scaler = MinMaxScaler()
                    result[col] = scaler.fit_transform(result[[col]]).flatten()
                else:
                    # Manual min-max scaling
                    col_min, col_max = result[col].min(), result[col].max()
                    result[col] = (result[col] - col_min) / (col_max - col_min)
            elif method == 'z_score':
                result[col] = (result[col] - result[col].mean()) / result[col].std()
            elif method == 'custom' and custom_range:
                min_val, max_val = custom_range
                data_min, data_max = result[col].min(), result[col].max()
                result[col] = min_val + (result[col] - data_min) * (max_val - min_val) / (data_max - data_min)
        
        return result
    
    @staticmethod
    def remove_outliers(data, method='iqr', threshold=1.5, column=None):
        """
        Remove outliers from data
        
        Args:
            data: pandas DataFrame
            method: 'iqr', 'z_score', 'modified_z_score'
            threshold: threshold value for outlier detection
            column: specific column to process (None for all numeric columns)
        
        Returns:
            pandas DataFrame with outliers removed
        """
        result = data.copy()
        
        if column:
            columns_to_process = [column] if column in data.columns else []
        else:
            columns_to_process = data.select_dtypes(include=[np.number]).columns
        
        mask = pd.Series([True] * len(result))
        
        for col in columns_to_process:
            if method == 'iqr':
                Q1 = result[col].quantile(0.25)
                Q3 = result[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - threshold * IQR
                upper_bound = Q3 + threshold * IQR
                mask &= (result[col] >= lower_bound) & (result[col] <= upper_bound)
            
            elif method == 'z_score':
                if HAS_SCIPY:
                    z_scores = np.abs(stats.zscore(result[col]))
                    mask &= z_scores < threshold
                else:
                    # Manual z-score calculation
                    mean_val = result[col].mean()
                    std_val = result[col].std()
                    z_scores = np.abs((result[col] - mean_val) / std_val)
                    mask &= z_scores < threshold
            
            elif method == 'modified_z_score':
                median = result[col].median()
                mad = np.median(np.abs(result[col] - median))
                modified_z_scores = 0.6745 * (result[col] - median) / mad
                mask &= np.abs(modified_z_scores) < threshold
        
        return result[mask].reset_index(drop=True)
    
    @staticmethod
    def smooth_data(data, method='moving_average', window_size=5, column=None, **kwargs):
        """
        Smooth data using various methods
        
        Args:
            data: pandas DataFrame
            method: 'moving_average', 'savgol', 'exponential'
            window_size: window size for smoothing
            column: specific column to smooth (None for all numeric columns)
            **kwargs: additional parameters for specific methods
        
        Returns:
            pandas DataFrame with smoothed data
        """
        result = data.copy()
        
        if column:
            columns_to_process = [column] if column in data.columns else []
        else:
            columns_to_process = data.select_dtypes(include=[np.number]).columns
        
        for col in columns_to_process:
            if method == 'moving_average':
                result[col] = result[col].rolling(window=window_size, center=True).mean()
            
            elif method == 'savgol':
                if HAS_SCIPY:
                    polyorder = kwargs.get('polyorder', 3)
                    if window_size > len(result):
                        window_size = len(result) if len(result) % 2 == 1 else len(result) - 1
                    if polyorder >= window_size:
                        polyorder = window_size - 1
                    result[col] = signal.savgol_filter(result[col], window_size, polyorder)
                else:
                    # Fallback to moving average if scipy not available
                    result[col] = result[col].rolling(window=window_size, center=True).mean()
            
            elif method == 'exponential':
                alpha = kwargs.get('alpha', 0.3)
                result[col] = result[col].ewm(alpha=alpha).mean()
        
        return result
    
    @staticmethod
    def filter_data(data, filter_type='lowpass', cutoff=0.1, order=4, column=None):
        """
        Apply digital filters to data
        
        Args:
            data: pandas DataFrame
            filter_type: 'lowpass', 'highpass', 'bandpass', 'bandstop'
            cutoff: cutoff frequency (or frequencies for bandpass/bandstop)
            order: filter order
            column: specific column to filter (None for all numeric columns)
        
        Returns:
            pandas DataFrame with filtered data
        """
        result = data.copy()
        
        if column:
            columns_to_process = [column] if column in data.columns else []
        else:
            columns_to_process = data.select_dtypes(include=[np.number]).columns
        
        for col in columns_to_process:
            try:
                if HAS_SCIPY:
                    if filter_type in ['lowpass', 'highpass']:
                        b, a = signal.butter(order, cutoff, btype=filter_type)
                    elif filter_type in ['bandpass', 'bandstop']:
                        if isinstance(cutoff, (list, tuple)) and len(cutoff) == 2:
                            b, a = signal.butter(order, cutoff, btype=filter_type)
                        else:
                            continue  # Skip if cutoff is not properly formatted

                    result[col] = signal.filtfilt(b, a, result[col])
                else:
                    # Simple moving average as fallback filter
                    window = max(3, int(1/cutoff)) if cutoff > 0 else 5
                    result[col] = result[col].rolling(window=window, center=True).mean()
            except Exception:
                # Skip filtering if it fails
                continue
        
        return result
    
    @staticmethod
    def scale_data(data, method='standard', column=None):
        """
        Scale data using various methods
        
        Args:
            data: pandas DataFrame
            method: 'standard', 'robust', 'min_max'
            column: specific column to scale (None for all numeric columns)
        
        Returns:
            pandas DataFrame with scaled data
        """
        result = data.copy()
        
        if column:
            columns_to_process = [column] if column in data.columns else []
        else:
            columns_to_process = data.select_dtypes(include=[np.number]).columns
        
        for col in columns_to_process:
            if HAS_SKLEARN:
                if method == 'standard':
                    scaler = StandardScaler()
                elif method == 'robust':
                    scaler = RobustScaler()
                elif method == 'min_max':
                    scaler = MinMaxScaler()

                result[col] = scaler.fit_transform(result[[col]]).flatten()
            else:
                # Manual scaling implementations
                if method == 'standard':
                    result[col] = (result[col] - result[col].mean()) / result[col].std()
                elif method == 'robust':
                    median = result[col].median()
                    mad = np.median(np.abs(result[col] - median))
                    result[col] = (result[col] - median) / mad
                elif method == 'min_max':
                    col_min, col_max = result[col].min(), result[col].max()
                    result[col] = (result[col] - col_min) / (col_max - col_min)
        
        return result
    
    @staticmethod
    def interpolate_missing(data, method='linear', column=None):
        """
        Interpolate missing values
        
        Args:
            data: pandas DataFrame
            method: 'linear', 'polynomial', 'spline', 'nearest'
            column: specific column to interpolate (None for all numeric columns)
        
        Returns:
            pandas DataFrame with interpolated data
        """
        result = data.copy()
        
        if column:
            columns_to_process = [column] if column in data.columns else []
        else:
            columns_to_process = data.select_dtypes(include=[np.number]).columns
        
        for col in columns_to_process:
            if method == 'linear':
                result[col] = result[col].interpolate(method='linear')
            elif method == 'polynomial':
                result[col] = result[col].interpolate(method='polynomial', order=2)
            elif method == 'spline':
                result[col] = result[col].interpolate(method='spline', order=3)
            elif method == 'nearest':
                result[col] = result[col].interpolate(method='nearest')
        
        return result
    
    @staticmethod
    def remove_duplicates(data, subset=None, keep='first'):
        """
        Remove duplicate rows
        
        Args:
            data: pandas DataFrame
            subset: columns to consider for duplicates (None for all columns)
            keep: 'first', 'last', False
        
        Returns:
            pandas DataFrame with duplicates removed
        """
        return data.drop_duplicates(subset=subset, keep=keep).reset_index(drop=True)
    
    @staticmethod
    def sort_data(data, by_column, ascending=True):
        """
        Sort data by specified column
        
        Args:
            data: pandas DataFrame
            by_column: column name to sort by
            ascending: sort order
        
        Returns:
            pandas DataFrame sorted by specified column
        """
        if by_column in data.columns:
            return data.sort_values(by=by_column, ascending=ascending).reset_index(drop=True)
        return data
    
    @staticmethod
    def transpose_data(data):
        """
        Transpose data (useful for 2D/3D data)

        Args:
            data: pandas DataFrame

        Returns:
            pandas DataFrame transposed
        """
        return data.T.reset_index(drop=True)

    # ==================== ADVANCED PROCESSING METHODS ====================

    @staticmethod
    def crop_data(data, x_column=None, x_min=None, x_max=None, y_column=None, y_min=None, y_max=None):
        """
        Crop data to specified ranges

        Args:
            data: pandas DataFrame
            x_column: column name for X-axis cropping
            x_min, x_max: X-axis range limits
            y_column: column name for Y-axis cropping
            y_min, y_max: Y-axis range limits

        Returns:
            pandas DataFrame with cropped data
        """
        result = data.copy()

        # X-axis cropping
        if x_column and x_column in data.columns:
            if x_min is not None:
                result = result[result[x_column] >= x_min]
            if x_max is not None:
                result = result[result[x_column] <= x_max]

        # Y-axis cropping
        if y_column and y_column in data.columns:
            if y_min is not None:
                result = result[result[y_column] >= y_min]
            if y_max is not None:
                result = result[result[y_column] <= y_max]

        return result.reset_index(drop=True)

    @staticmethod
    def shift_data(data, column=None, x_shift=0, y_shift=0):
        """
        Shift data by specified amounts

        Args:
            data: pandas DataFrame
            column: specific column to shift (None for all numeric)
            x_shift: horizontal shift amount
            y_shift: vertical shift amount

        Returns:
            pandas DataFrame with shifted data
        """
        result = data.copy()

        if column:
            columns_to_process = [column] if column in data.columns else []
        else:
            columns_to_process = data.select_dtypes(include=[np.number]).columns

        # Apply vertical shift
        for col in columns_to_process:
            result[col] = result[col] + y_shift

        # Apply horizontal shift (for first column, typically X-axis)
        if len(columns_to_process) > 0 and x_shift != 0:
            first_col = columns_to_process[0]
            result[first_col] = result[first_col] + x_shift

        return result

    @staticmethod
    def create_baseline(data, x_column, y_column, method='linear', degree=1, points=None):
        """
        Create baseline for data

        Args:
            data: pandas DataFrame
            x_column: X-axis column name
            y_column: Y-axis column name
            method: 'linear', 'polynomial', 'spline', 'manual_points'
            degree: polynomial degree for polynomial method
            points: list of (x, y) points for manual baseline

        Returns:
            pandas DataFrame with added baseline column
        """
        result = data.copy()

        if x_column not in data.columns or y_column not in data.columns:
            return result

        x_data = data[x_column].values
        y_data = data[y_column].values

        if method == 'linear':
            # Linear baseline from first to last point
            baseline = np.linspace(y_data[0], y_data[-1], len(y_data))

        elif method == 'polynomial':
            # Polynomial fit
            coeffs = np.polyfit(x_data, y_data, degree)
            baseline = np.polyval(coeffs, x_data)

        elif method == 'spline' and HAS_SCIPY:
            # Spline interpolation
            from scipy.interpolate import UnivariateSpline
            spline = UnivariateSpline(x_data, y_data, s=len(x_data))
            baseline = spline(x_data)

        elif method == 'manual_points' and points:
            # Manual baseline from specified points
            points_x = [p[0] for p in points]
            points_y = [p[1] for p in points]
            baseline = np.interp(x_data, points_x, points_y)

        else:
            # Fallback to linear
            baseline = np.linspace(y_data[0], y_data[-1], len(y_data))

        result[f'{y_column}_baseline'] = baseline
        return result

    @staticmethod
    def subtract_baseline(data, y_column, baseline_column=None):
        """
        Subtract baseline from data

        Args:
            data: pandas DataFrame
            y_column: column to subtract baseline from
            baseline_column: baseline column name (auto-detected if None)

        Returns:
            pandas DataFrame with baseline-corrected data
        """
        result = data.copy()

        if baseline_column is None:
            baseline_column = f'{y_column}_baseline'

        if y_column in data.columns and baseline_column in data.columns:
            result[f'{y_column}_corrected'] = result[y_column] - result[baseline_column]

        return result

    @staticmethod
    def subtract_data(data, column1, column2, result_column=None):
        """
        Subtract one data column from another

        Args:
            data: pandas DataFrame
            column1: first column (minuend)
            column2: second column (subtrahend)
            result_column: name for result column

        Returns:
            pandas DataFrame with subtraction result
        """
        result = data.copy()

        if column1 in data.columns and column2 in data.columns:
            if result_column is None:
                result_column = f'{column1}_minus_{column2}'

            result[result_column] = result[column1] - result[column2]

        return result

    @staticmethod
    def fourier_transform(data, x_column, y_column, transform_type='fft'):
        """
        Apply Fourier transform to data

        Args:
            data: pandas DataFrame
            x_column: X-axis column (for frequency calculation)
            y_column: Y-axis column to transform
            transform_type: 'fft', 'ifft', 'power_spectrum'

        Returns:
            pandas DataFrame with transform results
        """
        result = data.copy()

        if x_column not in data.columns or y_column not in data.columns:
            return result

        y_data = data[y_column].values
        x_data = data[x_column].values

        # Calculate sampling frequency
        if len(x_data) > 1:
            dt = np.mean(np.diff(x_data))
            fs = 1.0 / dt
        else:
            fs = 1.0

        if transform_type == 'fft':
            # Forward FFT
            fft_result = np.fft.fft(y_data)
            freqs = np.fft.fftfreq(len(y_data), dt)

            result = pd.DataFrame({
                'Frequency': freqs[:len(freqs)//2],
                'Magnitude': np.abs(fft_result)[:len(fft_result)//2],
                'Phase': np.angle(fft_result)[:len(fft_result)//2],
                'Real': np.real(fft_result)[:len(fft_result)//2],
                'Imaginary': np.imag(fft_result)[:len(fft_result)//2]
            })

        elif transform_type == 'ifft':
            # Inverse FFT (assuming y_data is complex)
            ifft_result = np.fft.ifft(y_data)
            result[f'{y_column}_ifft_real'] = np.real(ifft_result)
            result[f'{y_column}_ifft_imag'] = np.imag(ifft_result)

        elif transform_type == 'power_spectrum':
            # Power spectral density
            fft_result = np.fft.fft(y_data)
            freqs = np.fft.fftfreq(len(y_data), dt)
            power = np.abs(fft_result)**2

            result = pd.DataFrame({
                'Frequency': freqs[:len(freqs)//2],
                'Power': power[:len(power)//2]
            })

        return result

    @staticmethod
    def deconvolute_data(data, signal_column, response_column, method='wiener', noise_level=0.1):
        """
        Deconvolute signal from response function

        Args:
            data: pandas DataFrame
            signal_column: measured signal column
            response_column: instrument response column
            method: 'wiener', 'richardson_lucy' (if available)
            noise_level: noise level for Wiener deconvolution

        Returns:
            pandas DataFrame with deconvoluted result
        """
        result = data.copy()

        if signal_column not in data.columns or response_column not in data.columns:
            return result

        signal = data[signal_column].values
        response = data[response_column].values

        if method == 'wiener':
            # Wiener deconvolution in frequency domain
            signal_fft = np.fft.fft(signal)
            response_fft = np.fft.fft(response, len(signal))

            # Wiener filter
            response_conj = np.conj(response_fft)
            response_power = np.abs(response_fft)**2
            wiener_filter = response_conj / (response_power + noise_level**2)

            deconv_fft = signal_fft * wiener_filter
            deconvoluted = np.real(np.fft.ifft(deconv_fft))

            result[f'{signal_column}_deconv'] = deconvoluted

        return result

    @staticmethod
    def find_peaks(data, x_column, y_column, height=None, distance=None, prominence=None):
        """
        Find peaks in data

        Args:
            data: pandas DataFrame
            x_column: X-axis column name
            y_column: Y-axis column name
            height: minimum peak height
            distance: minimum distance between peaks
            prominence: minimum peak prominence

        Returns:
            pandas DataFrame with peak information
        """
        if x_column not in data.columns or y_column not in data.columns:
            return pd.DataFrame()

        x_data = data[x_column].values
        y_data = data[y_column].values

        if HAS_SCIPY:
            from scipy.signal import find_peaks as scipy_find_peaks

            # Find peaks using scipy
            peaks, properties = scipy_find_peaks(
                y_data,
                height=height,
                distance=distance,
                prominence=prominence
            )

            peak_data = pd.DataFrame({
                'Peak_Index': peaks,
                'X_Position': x_data[peaks],
                'Y_Height': y_data[peaks]
            })

            # Add properties if available
            if 'prominences' in properties:
                peak_data['Prominence'] = properties['prominences']
            if 'widths' in properties:
                peak_data['Width'] = properties['widths']

        else:
            # Simple peak finding without scipy
            peaks = []
            for i in range(1, len(y_data) - 1):
                if y_data[i] > y_data[i-1] and y_data[i] > y_data[i+1]:
                    if height is None or y_data[i] >= height:
                        peaks.append(i)

            peak_data = pd.DataFrame({
                'Peak_Index': peaks,
                'X_Position': x_data[peaks] if peaks else [],
                'Y_Height': y_data[peaks] if peaks else []
            })

        return peak_data

    @staticmethod
    def integrate_peaks(data, x_column, y_column, peak_ranges=None, method='trapz'):
        """
        Integrate over specified peak ranges

        Args:
            data: pandas DataFrame
            x_column: X-axis column name
            y_column: Y-axis column name
            peak_ranges: list of (x_start, x_end) tuples for integration ranges
            method: 'trapz', 'simpson' (if available)

        Returns:
            pandas DataFrame with integration results
        """
        if x_column not in data.columns or y_column not in data.columns:
            return pd.DataFrame()

        if peak_ranges is None:
            return pd.DataFrame()

        x_data = data[x_column].values
        y_data = data[y_column].values

        integration_results = []

        for i, (x_start, x_end) in enumerate(peak_ranges):
            # Find indices for integration range
            mask = (x_data >= x_start) & (x_data <= x_end)
            x_range = x_data[mask]
            y_range = y_data[mask]

            if len(x_range) > 1:
                if method == 'trapz':
                    integral = np.trapz(y_range, x_range)
                elif method == 'simpson' and HAS_SCIPY:
                    from scipy.integrate import simpson
                    integral = simpson(y_range, x_range)
                else:
                    integral = np.trapz(y_range, x_range)

                integration_results.append({
                    'Peak_Number': i + 1,
                    'X_Start': x_start,
                    'X_End': x_end,
                    'Integral': integral,
                    'Peak_Area': integral
                })

        return pd.DataFrame(integration_results)

    @staticmethod
    def custom_formula(data, formula, new_column_name='Custom_Result'):
        """
        Apply custom formula to data

        Args:
            data: pandas DataFrame
            formula: string formula using column names (e.g., "A + B * 2")
            new_column_name: name for the result column

        Returns:
            pandas DataFrame with custom calculation result
        """
        result = data.copy()

        try:
            # Create a safe evaluation environment
            safe_dict = {
                'np': np,
                'sin': np.sin, 'cos': np.cos, 'tan': np.tan,
                'exp': np.exp, 'log': np.log, 'log10': np.log10,
                'sqrt': np.sqrt, 'abs': np.abs,
                'pi': np.pi, 'e': np.e
            }

            # Add column data to evaluation environment
            for col in data.columns:
                if col.replace('_', '').replace(' ', '').isalnum():  # Safe column names
                    safe_dict[col] = data[col].values

            # Evaluate the formula
            result[new_column_name] = eval(formula, {"__builtins__": {}}, safe_dict)

        except Exception as e:
            # If formula fails, add error column
            result[new_column_name] = f"Error: {str(e)}"

        return result

    @staticmethod
    def advanced_smoothing(data, column, method='gaussian', window_size=5, **kwargs):
        """
        Advanced smoothing methods

        Args:
            data: pandas DataFrame
            column: column to smooth
            method: 'gaussian', 'median', 'bilateral', 'lowess'
            window_size: smoothing window size
            **kwargs: additional parameters for specific methods

        Returns:
            pandas DataFrame with smoothed data
        """
        result = data.copy()

        if column not in data.columns:
            return result

        y_data = data[column].values

        if method == 'gaussian' and HAS_SCIPY:
            from scipy.ndimage import gaussian_filter1d
            sigma = kwargs.get('sigma', window_size / 3.0)
            smoothed = gaussian_filter1d(y_data, sigma=sigma)

        elif method == 'median':
            # Median filter
            if HAS_SCIPY:
                from scipy.signal import medfilt
                smoothed = medfilt(y_data, kernel_size=window_size)
            else:
                # Simple median filter
                smoothed = pd.Series(y_data).rolling(window=window_size, center=True).median().values

        elif method == 'bilateral' and HAS_SCIPY:
            # Bilateral filter (edge-preserving)
            from scipy.ndimage import gaussian_filter1d
            sigma_spatial = kwargs.get('sigma_spatial', window_size / 3.0)
            sigma_intensity = kwargs.get('sigma_intensity', 0.1)
            # Simplified bilateral filter
            smoothed = gaussian_filter1d(y_data, sigma=sigma_spatial)

        elif method == 'lowess':
            # LOWESS smoothing
            try:
                import statsmodels.api as sm
                frac = kwargs.get('frac', 0.1)
                x_data = np.arange(len(y_data))
                lowess_result = sm.nonparametric.lowess(y_data, x_data, frac=frac)
                smoothed = lowess_result[:, 1]
            except ImportError:
                # Fallback to moving average
                smoothed = pd.Series(y_data).rolling(window=window_size, center=True).mean().values

        else:
            # Fallback to moving average
            smoothed = pd.Series(y_data).rolling(window=window_size, center=True).mean().values

        result[f'{column}_smoothed'] = smoothed
        return result

    @staticmethod
    def advanced_interpolation(data, x_column, y_column, method='cubic', num_points=None, x_new=None):
        """
        Advanced interpolation methods

        Args:
            data: pandas DataFrame
            x_column: X-axis column name
            y_column: Y-axis column name
            method: 'cubic', 'akima', 'pchip', 'rbf'
            num_points: number of interpolated points (if x_new not provided)
            x_new: new X values for interpolation

        Returns:
            pandas DataFrame with interpolated data
        """
        if x_column not in data.columns or y_column not in data.columns:
            return data.copy()

        x_data = data[x_column].values
        y_data = data[y_column].values

        # Remove NaN values
        mask = ~(np.isnan(x_data) | np.isnan(y_data))
        x_clean = x_data[mask]
        y_clean = y_data[mask]

        if len(x_clean) < 2:
            return data.copy()

        # Define new X values
        if x_new is None:
            if num_points is None:
                num_points = len(x_clean) * 2
            x_new = np.linspace(x_clean.min(), x_clean.max(), num_points)

        if HAS_SCIPY:
            from scipy.interpolate import interp1d, Akima1DInterpolator, PchipInterpolator

            try:
                if method == 'cubic':
                    f = interp1d(x_clean, y_clean, kind='cubic', fill_value='extrapolate')
                    y_new = f(x_new)

                elif method == 'akima':
                    f = Akima1DInterpolator(x_clean, y_clean)
                    y_new = f(x_new)

                elif method == 'pchip':
                    f = PchipInterpolator(x_clean, y_clean)
                    y_new = f(x_new)

                elif method == 'rbf':
                    from scipy.interpolate import Rbf
                    rbf = Rbf(x_clean, y_clean, function='multiquadric')
                    y_new = rbf(x_new)

                else:
                    # Linear interpolation
                    y_new = np.interp(x_new, x_clean, y_clean)

            except Exception:
                # Fallback to linear
                y_new = np.interp(x_new, x_clean, y_clean)

        else:
            # Simple linear interpolation
            y_new = np.interp(x_new, x_clean, y_clean)

        result = pd.DataFrame({
            f'{x_column}_interp': x_new,
            f'{y_column}_interp': y_new
        })

        return result
