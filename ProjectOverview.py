#!/usr/bin/env python3
"""
Project Overview Window - Standalone module for browsing and managing projects
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import json
import datetime
import shutil
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False


class ProjectOverviewWindow:
    """Project Overview Window with tree view and preview functionality"""
    
    def __init__(self, parent, project_directory, main_app=None):
        self.parent = parent
        self.project_directory = project_directory
        self.main_app = main_app
        self.selected_items = set()  # Track checked items
        self.current_preview_item = None
        self.preview_photo = None  # Keep reference to prevent garbage collection
        
        # Create window
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("Project Overview")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)
        
        # Make window modal if parent exists
        if parent:
            self.window.transient(parent)
            self.window.grab_set()
        
        # Center window
        self.center_window()
        
        # Setup UI
        self.setup_ui()
        
        # Load projects
        self.load_projects()
        
        # Handle window closing
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main container
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create paned window for left/right split
        paned_window = ttk.PanedWindow(main_frame, orient="horizontal")
        paned_window.pack(fill="both", expand=True)
        
        # Left panel for tree view and buttons
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)
        
        # Right panel for preview and comments
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=1)
        
        # Setup left panel
        self.setup_left_panel(left_frame)
        
        # Setup right panel
        self.setup_right_panel(right_frame)
    
    def setup_left_panel(self, parent):
        """Setup left panel with tree view and action buttons"""
        # Tree view frame
        tree_frame = ttk.LabelFrame(parent, text="Projects and Measurements", padding="5")
        tree_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # Create tree view with scrollbars
        tree_container = ttk.Frame(tree_frame)
        tree_container.pack(fill="both", expand=True)
        
        # Tree view with checkbox column
        self.tree = ttk.Treeview(tree_container, columns=("checkbox", "type", "date"), show="tree headings", selectmode="browse")
        self.tree.heading("#0", text="Name")
        self.tree.heading("checkbox", text="☐")
        self.tree.heading("type", text="Type")
        self.tree.heading("date", text="Modified")

        self.tree.column("#0", width=250)
        self.tree.column("checkbox", width=30)
        self.tree.column("type", width=100)
        self.tree.column("date", width=150)
        
        # Scrollbars for tree
        v_scrollbar = ttk.Scrollbar(tree_container, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_container, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack tree and scrollbars
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        tree_container.grid_rowconfigure(0, weight=1)
        tree_container.grid_columnconfigure(0, weight=1)
        
        # Bind tree events
        self.tree.bind("<Button-1>", self.on_tree_click)
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)
        
        # Action buttons frame
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill="x", pady=(5, 0))
        
        # Action buttons
        ttk.Button(button_frame, text="Merge", command=self.merge_selected).pack(side="left", padx=(0, 5))
        ttk.Button(button_frame, text="Delete", command=self.delete_selected).pack(side="left", padx=(0, 5))
        ttk.Button(button_frame, text="Export", command=self.export_selected).pack(side="left", padx=(0, 5))
        
        # Close button
        ttk.Button(button_frame, text="Close", command=self.on_closing).pack(side="right")
    
    def setup_right_panel(self, parent):
        """Setup right panel with preview and comments"""
        # Preview frame
        preview_frame = ttk.LabelFrame(parent, text="Preview", padding="5")
        preview_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # Preview canvas with scrollbars
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.pack(fill="both", expand=True)
        
        self.preview_canvas = tk.Canvas(canvas_frame, bg="white", relief="sunken", bd=1)
        
        # Scrollbars for canvas
        v_scroll_preview = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.preview_canvas.yview)
        h_scroll_preview = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.preview_canvas.xview)
        self.preview_canvas.configure(yscrollcommand=v_scroll_preview.set, xscrollcommand=h_scroll_preview.set)
        
        # Pack canvas and scrollbars
        self.preview_canvas.grid(row=0, column=0, sticky="nsew")
        v_scroll_preview.grid(row=0, column=1, sticky="ns")
        h_scroll_preview.grid(row=1, column=0, sticky="ew")
        
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # Comments frame
        comments_frame = ttk.LabelFrame(parent, text="Comments", padding="5")
        comments_frame.pack(fill="x", pady=(0, 10))
        
        # Comments text widget with scrollbar
        comment_container = ttk.Frame(comments_frame)
        comment_container.pack(fill="both", expand=True)
        
        self.comment_text = tk.Text(comment_container, height=6, wrap="word")
        comment_scroll = ttk.Scrollbar(comment_container, orient="vertical", command=self.comment_text.yview)
        self.comment_text.configure(yscrollcommand=comment_scroll.set)
        
        self.comment_text.pack(side="left", fill="both", expand=True)
        comment_scroll.pack(side="right", fill="y")
        
        # Edit button frame
        edit_frame = ttk.Frame(parent)
        edit_frame.pack(fill="x")
        
        ttk.Button(edit_frame, text="Edit", command=self.edit_selected).pack(side="left")
        ttk.Button(edit_frame, text="Save Comment", command=self.save_comment).pack(side="left", padx=(5, 0))

    def load_projects(self):
        """Load projects from project directory"""
        if not os.path.exists(self.project_directory):
            return

        try:
            for project_name in os.listdir(self.project_directory):
                project_path = os.path.join(self.project_directory, project_name)

                if os.path.isdir(project_path):
                    # Add project node
                    project_item = self.tree.insert("", "end", text=project_name,
                                                   values=("☐", "Project", self.get_modification_date(project_path)))

                    # Add measurement nodes
                    for measurement_name in os.listdir(project_path):
                        measurement_path = os.path.join(project_path, measurement_name)

                        if os.path.isdir(measurement_path):
                            # Check if it has plot_config.json
                            config_file = os.path.join(measurement_path, "plot_config.json")
                            if os.path.exists(config_file):
                                self.tree.insert(project_item, "end", text=measurement_name,
                                               values=("☐", "Measurement", self.get_modification_date(measurement_path)))

                    # Expand project nodes
                    self.tree.item(project_item, open=True)

        except Exception as e:
            messagebox.showerror("Error", f"Could not load projects: {e}")

    def get_modification_date(self, path):
        """Get modification date of path"""
        try:
            timestamp = os.path.getmtime(path)
            return datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M")
        except:
            return "Unknown"

    def on_tree_click(self, event):
        """Handle tree click events"""
        region = self.tree.identify_region(event.x, event.y)
        column = self.tree.identify("column", event.x, event.y)
        item = self.tree.identify("item", event.x, event.y)

        if item and column == "#1":  # Checkbox column
            self.toggle_checkbox(item)
        elif item and region == "cell":
            # Select the item (this will trigger the selection event)
            self.tree.selection_set(item)
            self.tree.focus(item)

    def on_tree_select(self, event):
        """Handle tree selection events"""
        selection = self.tree.selection()
        if selection:
            self.load_preview(selection[0])

    def toggle_checkbox(self, item):
        """Toggle checkbox for item"""
        current_values = list(self.tree.item(item, "values"))

        if item in self.selected_items:
            # Uncheck
            self.selected_items.remove(item)
            current_values[0] = "☐"
        else:
            # Check
            self.selected_items.add(item)
            current_values[0] = "☑"

        self.tree.item(item, values=current_values)

    def load_preview(self, item):
        """Load preview for selected item"""
        self.current_preview_item = item

        # Clear previous preview
        self.preview_canvas.delete("all")
        self.comment_text.delete(1.0, tk.END)

        # Get item path
        item_path = self.get_item_path(item)
        if not item_path:
            return

        # Load preview image
        preview_file = os.path.join(item_path, "preview.png")

        if os.path.exists(preview_file):
            try:
                if PIL_AVAILABLE:
                    from PIL import Image, ImageTk

                    # Load and display image
                    image = Image.open(preview_file)

                    # Resize if too large
                    max_size = (700, 500)
                    image.thumbnail(max_size, Image.Resampling.LANCZOS)

                    # Create PhotoImage and keep reference BEFORE clearing old one
                    new_photo = ImageTk.PhotoImage(image)

                    # Create image on canvas with the PhotoImage reference
                    self.preview_canvas.create_image(10, 10, anchor="nw", image=new_photo)

                    # Update canvas scroll region
                    self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))

                    # Force canvas update
                    self.preview_canvas.update_idletasks()

                    # Now safely update the reference (after image is displayed)
                    self.preview_photo = new_photo
                else:
                    self.preview_canvas.create_text(10, 10, anchor="nw",
                                                  text="PIL not available - cannot display image")

            except Exception as e:
                self.preview_canvas.create_text(10, 10, anchor="nw",
                                              text=f"Could not load preview: {e}")
        else:
            self.preview_canvas.create_text(10, 10, anchor="nw",
                                          text="No preview available")

        # Load comments
        self.load_comments(item_path)

    def get_item_path(self, item):
        """Get filesystem path for tree item"""
        # Build path from tree hierarchy
        path_parts = []
        current = item

        while current:
            path_parts.insert(0, self.tree.item(current, "text"))
            current = self.tree.parent(current)

        if len(path_parts) >= 1:
            return os.path.join(self.project_directory, *path_parts)
        return None

    def load_comments(self, item_path):
        """Load comments from plot_config.json"""
        config_file = os.path.join(item_path, "plot_config.json")
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)

                comment = config.get("comment", "")
                self.comment_text.insert(1.0, comment)

            except Exception as e:
                self.comment_text.insert(1.0, f"Error loading comment: {e}")

    def save_comment(self):
        """Save comment to plot_config.json"""
        if not self.current_preview_item:
            messagebox.showwarning("Warning", "No item selected")
            return

        item_path = self.get_item_path(self.current_preview_item)
        if not item_path:
            return

        config_file = os.path.join(item_path, "plot_config.json")

        try:
            # Load existing config or create new
            config = {}
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)

            # Update comment
            config["comment"] = self.comment_text.get(1.0, tk.END).strip()

            # Save config
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=4)

            messagebox.showinfo("Success", "Comment saved")

        except Exception as e:
            messagebox.showerror("Error", f"Could not save comment: {e}")

    def detect_data_dimensionality(self, data_file):
        """Detect if data is 2D or 3D based on column count and content"""
        try:
            import pandas as pd
            data = pd.read_csv(data_file)

            # Check number of columns
            num_cols = len(data.columns)

            # Basic heuristics for dimensionality detection
            if num_cols >= 3:
                # Check if we have X, Y, Z columns or similar patterns
                col_names = [col.lower() for col in data.columns]

                # Look for 3D indicators
                has_z = any('z' in col for col in col_names)
                has_3d_keywords = any(keyword in ' '.join(col_names) for keyword in ['depth', 'height', 'elevation'])

                # If we have Z column or 3D keywords, assume 3D
                if has_z or has_3d_keywords or num_cols > 3:
                    return "3D"

            # Default to 2D
            return "2D"

        except Exception as e:
            print(f"Error detecting dimensionality: {e}")
            return "2D"  # Default to 2D on error

    def edit_selected(self):
        """Edit selected measurement in appropriate plotting app"""
        if not self.current_preview_item:
            messagebox.showwarning("Warning", "No item selected")
            return

        item_path = self.get_item_path(self.current_preview_item)
        if not item_path:
            return

        # Check if it's a measurement (has plot_config.json)
        config_file = os.path.join(item_path, "plot_config.json")
        if not os.path.exists(config_file):
            messagebox.showwarning("Warning", "Selected item is not a measurement")
            return

        try:
            # Load plot config
            with open(config_file, 'r') as f:
                plot_config = json.load(f)

            # Load data
            data_dir = os.path.join(item_path, "Data")
            data_files = []
            if os.path.exists(data_dir):
                data_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]

            if not data_files:
                messagebox.showerror("Error", "No data files found in measurement")
                return

            # Load first data file (or let user choose if multiple)
            data_file = os.path.join(data_dir, data_files[0])

            import pandas as pd
            data = pd.read_csv(data_file)

            # Detect dimensionality
            dimensionality = self.detect_data_dimensionality(data_file)

            # Add project directory to config
            enhanced_config = plot_config.copy()
            enhanced_config['project_directory'] = self.project_directory

            # Launch appropriate plotting window based on dimensionality
            if dimensionality == "3D":
                from PlottingApp3D import Plot3DWindow

                # Create new window for 3D plotting
                plot_window = tk.Toplevel()
                plot_window.title(f"3D Plot - {self.tree.item(self.current_preview_item, 'text')}")
                plot_window.geometry("1200x800")

                # Create 3D plotting app
                Plot3DWindow(plot_window, data, enhanced_config, data_files[0])

            else:  # 2D
                from PlottingApp2D import Plot2DWindow

                # Create new window for 2D plotting
                plot_window = tk.Toplevel()
                plot_window.title(f"2D Plot - {self.tree.item(self.current_preview_item, 'text')}")
                plot_window.geometry("1400x900")

                # Create 2D plotting app
                Plot2DWindow(plot_window, data, enhanced_config, data_files[0])

            # Close this project overview window
            self.window.destroy()

            # Set up window closing handler to show main app when plotting window closes
            def on_plot_window_close():
                plot_window.destroy()
                if self.main_app and hasattr(self.main_app, 'root'):
                    self.main_app.root.deiconify()  # Show main app window

            plot_window.protocol("WM_DELETE_WINDOW", on_plot_window_close)

        except Exception as e:
            messagebox.showerror("Error", f"Could not open for editing: {e}")
            import traceback
            traceback.print_exc()

    def merge_selected(self):
        """Merge selected measurements"""
        if len(self.selected_items) < 2:
            messagebox.showwarning("Warning", "Please select at least 2 measurements to merge")
            return

        messagebox.showinfo("Info", "Merge functionality will be implemented in future version")

    def delete_selected(self):
        """Delete selected items"""
        if not self.selected_items:
            messagebox.showwarning("Warning", "No items selected")
            return

        # Confirm deletion
        count = len(self.selected_items)
        if not messagebox.askyesno("Confirm Deletion",
                                  f"Are you sure you want to delete {count} selected item(s)?\n\n"
                                  "This action cannot be undone."):
            return

        try:
            for item in list(self.selected_items):
                item_path = self.get_item_path(item)
                if item_path and os.path.exists(item_path):
                    shutil.rmtree(item_path)

                    # Remove from tree
                    self.tree.delete(item)
                    self.selected_items.remove(item)

            messagebox.showinfo("Success", f"Deleted {count} item(s)")

            # Clear preview if current item was deleted
            if self.current_preview_item not in [item for item in self.tree.get_children("")]:
                self.preview_canvas.delete("all")
                self.comment_text.delete(1.0, tk.END)
                self.current_preview_item = None

        except Exception as e:
            messagebox.showerror("Error", f"Could not delete items: {e}")

    def export_selected(self):
        """Export selected items"""
        if not self.selected_items:
            messagebox.showwarning("Warning", "No items selected")
            return

        messagebox.showinfo("Info", "Export functionality will be implemented in future version")

    def on_closing(self):
        """Handle window closing"""
        # Show main app window if it exists
        if self.main_app and hasattr(self.main_app, 'root'):
            self.main_app.root.deiconify()

        self.window.destroy()


def main():
    """Main function for standalone testing"""
    import tempfile

    # Create test project structure
    test_dir = tempfile.mkdtemp(prefix="test_projects_")

    # Create a simple test project
    project_dir = os.path.join(test_dir, "TestProject")
    measurement_dir = os.path.join(project_dir, "TestMeasurement")
    data_dir = os.path.join(measurement_dir, "Data")

    os.makedirs(data_dir, exist_ok=True)

    # Create plot config
    config = {
        "title": "Test Plot",
        "x_axis": "Time",
        "y_axis": "Value",
        "comment": "This is a test measurement."
    }

    with open(os.path.join(measurement_dir, "plot_config.json"), 'w') as f:
        json.dump(config, f, indent=4)

    # Create sample data
    with open(os.path.join(data_dir, "sample_data.csv"), 'w') as f:
        f.write("Time,Value\n1,10\n2,20\n3,15\n4,25\n5,30\n")

    # Create and run project overview
    root = tk.Tk()
    root.withdraw()  # Hide root window

    overview = ProjectOverviewWindow(None, test_dir)
    overview.window.mainloop()

    # Cleanup
    shutil.rmtree(test_dir)


if __name__ == "__main__":
    main()
