#!/usr/bin/env python3
"""
Test script for Project Overview Window functionality
"""

import tkinter as tk
import os
import json
import tempfile
import shutil
from pathlib import Path

# Create test project structure
def create_test_projects():
    """Create test project structure for testing"""
    test_dir = tempfile.mkdtemp(prefix="test_projects_")
    
    # Create Project 1
    project1_dir = os.path.join(test_dir, "TestProject1")
    measurement1_dir = os.path.join(project1_dir, "Measurement1")
    data1_dir = os.path.join(measurement1_dir, "Data")
    
    os.makedirs(data1_dir, exist_ok=True)
    
    # Create plot config for measurement 1
    config1 = {
        "title": "Test Plot 1",
        "x_axis": "Time",
        "y_axis": "Value",
        "comment": "This is a test measurement with some sample data."
    }
    
    with open(os.path.join(measurement1_dir, "plot_config.json"), 'w') as f:
        json.dump(config1, f, indent=4)
    
    # Create sample data file
    with open(os.path.join(data1_dir, "sample_data.csv"), 'w') as f:
        f.write("Time,Value\n1,10\n2,20\n3,15\n4,25\n5,30\n")
    
    # Create Project 2
    project2_dir = os.path.join(test_dir, "TestProject2")
    measurement2_dir = os.path.join(project2_dir, "Measurement2")
    data2_dir = os.path.join(measurement2_dir, "Data")
    
    os.makedirs(data2_dir, exist_ok=True)
    
    # Create plot config for measurement 2
    config2 = {
        "title": "Test Plot 2",
        "x_axis": "Frequency",
        "y_axis": "Amplitude",
        "comment": "Another test measurement for verification."
    }
    
    with open(os.path.join(measurement2_dir, "plot_config.json"), 'w') as f:
        json.dump(config2, f, indent=4)
    
    # Create sample data file
    with open(os.path.join(data2_dir, "frequency_data.csv"), 'w') as f:
        f.write("Frequency,Amplitude\n100,0.5\n200,0.8\n300,0.3\n400,0.9\n500,0.2\n")
    
    return test_dir

def test_project_overview():
    """Test the project overview window"""
    # Create test projects
    test_dir = create_test_projects()
    print(f"Created test projects in: {test_dir}")
    
    try:
        # Import the main application
        from DataAnalysisApp import ProjectOverviewWindow
        
        # Create root window
        root = tk.Tk()
        root.withdraw()  # Hide main window
        
        # Create a mock main app object
        class MockMainApp:
            def on_project_action(self, action, data=None):
                print(f"Project action: {action}")
        
        mock_app = MockMainApp()
        
        # Create project overview window
        overview = ProjectOverviewWindow(root, test_dir, mock_app)
        
        print("Project Overview Window created successfully!")
        print("You should see:")
        print("- A tree view with TestProject1 and TestProject2")
        print("- Each project should have measurement subfolders")
        print("- Clicking on measurements should show comments")
        print("- Checkboxes should be available for selection")
        
        # Run the GUI
        root.mainloop()
        
    except Exception as e:
        print(f"Error testing project overview: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up test directory
        try:
            shutil.rmtree(test_dir)
            print(f"Cleaned up test directory: {test_dir}")
        except:
            print(f"Could not clean up test directory: {test_dir}")

if __name__ == "__main__":
    test_project_overview()
