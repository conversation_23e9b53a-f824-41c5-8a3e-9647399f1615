#!/usr/bin/env python3
"""
Test script for directory management functionality in DataAnalysisApp
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add current directory to path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from DataAnalysisApp import check_directory_permissions, get_default_project_directory

def test_directory_permissions():
    """Test directory permission checking"""
    print("Testing directory permission checking...")
    
    # Test with a valid directory (temp)
    temp_dir = tempfile.mkdtemp()
    print(f"Testing with temp directory: {temp_dir}")
    
    result = check_directory_permissions(temp_dir)
    print(f"Permission check result: {result}")
    assert result == True, "Should have permissions for temp directory"
    
    # Clean up
    shutil.rmtree(temp_dir)
    print("✅ Directory permission test passed")

def test_default_project_directory():
    """Test default project directory function"""
    print("\nTesting default project directory...")
    
    default_dir = get_default_project_directory()
    print(f"Default project directory: {default_dir}")
    
    expected_path = str(Path.home() / "Documents" / "PlottingApp_Projects")
    assert default_dir == expected_path, f"Expected {expected_path}, got {default_dir}"
    print("✅ Default project directory test passed")

def test_config_integration():
    """Test config integration"""
    print("\nTesting config integration...")
    
    # Import DataAnalysisApp class
    from DataAnalysisApp import DataAnalysisApp
    
    # Create a temporary config file
    import json
    import tempfile
    
    # Test config loading with project directory
    test_config = {
        "project_directory": str(Path.home() / "Documents" / "TestPlottingProjects"),
        "recent_projects": [],
        "default_import_directory": str(Path.home()),
    }
    
    # Create temporary config file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_config, f, indent=4)
        temp_config_file = f.name
    
    try:
        # Test loading config (without creating full app)
        print(f"Testing config loading from: {temp_config_file}")
        
        # Read back the config
        with open(temp_config_file, 'r') as f:
            loaded_config = json.load(f)
        
        assert 'project_directory' in loaded_config
        print(f"Config project directory: {loaded_config['project_directory']}")
        print("✅ Config integration test passed")
        
    finally:
        # Clean up
        os.unlink(temp_config_file)

def main():
    """Run all tests"""
    print("=" * 50)
    print("Testing Directory Management Functionality")
    print("=" * 50)
    
    try:
        test_directory_permissions()
        test_default_project_directory()
        test_config_integration()
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed!")
        print("Directory management functionality is working correctly.")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
